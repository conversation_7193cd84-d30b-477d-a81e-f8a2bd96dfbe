using System;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using AutoMapper;
using Microsoft.EntityFrameworkCore;
using NafaPlace.Catalog.Application.Common.Interfaces;
using NafaPlace.Catalog.Application.DTOs;
using NafaPlace.Catalog.Application.DTOs.Product;
using NafaPlace.Catalog.Domain.Models;
using Microsoft.Extensions.Logging;
using Microsoft.AspNetCore.Http;
using Azure.Storage.Blobs;
using System.IO;

namespace NafaPlace.Catalog.Application.Services
{
    public class ProductService : IProductService
    {
        private readonly ICatalogDbContext _context;
        private readonly IMapper _mapper;
        private readonly ILogger<ProductService> _logger;
        private readonly BlobServiceClient _blobServiceClient;

        public ProductService(ICatalogDbContext context, IMapper mapper, ILogger<ProductService> logger, BlobServiceClient blobServiceClient)
        {
            _context = context;
            _mapper = mapper;
            _logger = logger;
            _blobServiceClient = blobServiceClient;
        }

        public async Task<ProductDto> CreateProductAsync(CreateProductRequest request)
        {
            if (!request.SellerId.HasValue || request.SellerId.Value <= 0)
            {
                throw new ArgumentException("Un vendeur valide est requis.", nameof(request.SellerId));
            }

            var product = new Product
            {
                Name = request.Name,
                Description = request.Description,
                Price = request.Price,
                CategoryId = request.CategoryId ?? 1,
                StockQuantity = request.StockQuantity,
                Currency = request.Currency,
                Brand = request.Brand,
                Model = request.Model,
                Weight = request.Weight,
                Dimensions = request.Dimensions,
                IsActive = true,
                IsFeatured = request.IsFeatured,
                SellerId = request.SellerId.Value,
                ApprovalStatus = Domain.Enums.ProductApprovalStatus.Pending, // Par défaut en attente
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            await _context.Products.AddAsync(product);
            await _context.SaveChangesAsync();

            if (request.Images != null && request.Images.Any())
            {
                var isFirstImage = true;
                foreach (var imageFile in request.Images)
                {
                    var imageUrl = await UploadImageAsync(imageFile);
                    var image = new ProductImage
                    {
                        ProductId = product.Id,
                        ImageUrl = imageUrl,
                        ThumbnailUrl = imageUrl, // Ou une version miniature
                        IsMain = isFirstImage,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };
                    await _context.ProductImages.AddAsync(image);
                    isFirstImage = false;
                }
                await _context.SaveChangesAsync();
            }

            // Ajouter les attributs du produit
            if (request.Attributes != null && request.Attributes.Any())
            {
                foreach (var attributeRequest in request.Attributes)
                {
                    var attribute = new ProductAttribute
                    {
                        
                        ProductId = product.Id,
                        Name = attributeRequest.Name,
                        Value = attributeRequest.Value,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    await _context.ProductAttributes.AddAsync(attribute);
                }
                await _context.SaveChangesAsync();
            }

            // Ajouter les variantes du produit
            if (request.Variants != null && request.Variants.Any())
            {
                foreach (var variantRequest in request.Variants)
                {
                    var variant = new ProductVariant
                    {
                        
                        ProductId = product.Id,
                        Name = variantRequest.Name,
                        Sku = variantRequest.Sku,
                        Price = variantRequest.Price,
                        StockQuantity = variantRequest.StockQuantity,
                        IsActive = true,
                        CreatedAt = DateTime.UtcNow,
                        UpdatedAt = DateTime.UtcNow
                    };

                    await _context.ProductVariants.AddAsync(variant);
                }
                await _context.SaveChangesAsync();
            }

            return await GetProductByIdAsync(product.Id);
        }
        
        private async Task<string> UploadImageAsync(IFormFile imageFile)
        {
            var blobContainerClient = _blobServiceClient.GetBlobContainerClient("products");
            await blobContainerClient.CreateIfNotExistsAsync();

            var blobClient = blobContainerClient.GetBlobClient(Guid.NewGuid().ToString() + Path.GetExtension(imageFile.FileName));

            await using (var stream = imageFile.OpenReadStream())
            {
                await blobClient.UploadAsync(stream, true);
            }

            return blobClient.Uri.ToString();
        }

        public async Task<ProductDto> UpdateProductAsync(int id, UpdateProductRequest request)
        {
            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Attributes)
                .Include(p => p.Variants)
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                throw new Exception($"Product with ID {id} not found");
            }

            // Mettre à jour les propriétés de base du produit
            product.Name = request.Name;
            product.Description = request.Description;
            product.Price = request.Price;
            product.CategoryId = request.CategoryId ?? 1; // Utiliser 1 comme valeur par défaut au lieu de 0
            product.StockQuantity = request.StockQuantity;
            product.Currency = request.Currency;
            product.Brand = request.Brand;
            product.Model = request.Model;
            product.Weight = request.Weight;
            product.Dimensions = request.Dimensions;
            product.IsActive = request.IsActive;
            product.IsFeatured = request.IsFeatured;

            // Mettre à jour le statut d'approbation si fourni
            if (request.ApprovalStatus.HasValue)
            {
                product.ApprovalStatus = request.ApprovalStatus.Value;

                if (request.ApprovalStatus.Value == Domain.Enums.ProductApprovalStatus.Approved)
                {
                    product.ApprovedAt = DateTime.UtcNow;
                    product.ApprovedBy = request.ApprovedBy ?? "admin";
                    product.RejectionReason = null; // Effacer la raison de rejet
                }
                else if (request.ApprovalStatus.Value == Domain.Enums.ProductApprovalStatus.Rejected)
                {
                    product.RejectionReason = request.RejectionReason;
                    product.ApprovedAt = null;
                    product.ApprovedBy = request.ApprovedBy ?? "admin";
                }
                else if (request.ApprovalStatus.Value == Domain.Enums.ProductApprovalStatus.Pending)
                {
                    product.ApprovedAt = null;
                    product.ApprovedBy = null;
                    product.RejectionReason = null;
                }
            }

            product.UpdatedAt = DateTime.UtcNow;

            // Mettre à jour les attributs
            if (request.Attributes != null && request.Attributes.Any())
            {
                // Supprimer les attributs existants qui ne sont pas dans la nouvelle liste
                var attributeIdsToKeep = request.Attributes
                    .Where(a => a.Id.HasValue)
                    .Select(a => a.Id!.Value)
                    .ToList();

                var attributesToRemove = product.Attributes
                    .Where(a => !attributeIdsToKeep.Contains(a.Id))
                    .ToList();

                foreach (var attribute in attributesToRemove)
                {
                    _context.ProductAttributes.Remove(attribute);
                }

                // Mettre à jour les attributs existants et ajouter les nouveaux
                foreach (var attributeRequest in request.Attributes)
                {
                    if (attributeRequest.Id.HasValue)
                    {
                        // Mettre à jour un attribut existant
                        var existingAttribute = product.Attributes
                            .FirstOrDefault(a => a.Id == attributeRequest.Id.Value);

                        if (existingAttribute != null)
                        {
                            existingAttribute.Name = attributeRequest.Name;
                            existingAttribute.Value = attributeRequest.Value;
                            existingAttribute.UpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else
                    {
                        // Ajouter un nouvel attribut
                        var newAttribute = new ProductAttribute
                        {
                            
                            ProductId = product.Id,
                            Name = attributeRequest.Name,
                            Value = attributeRequest.Value,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        await _context.ProductAttributes.AddAsync(newAttribute);
                    }
                }
            }
            else
            {
                // Si la liste d'attributs est vide, supprimer tous les attributs existants
                foreach (var attribute in product.Attributes.ToList())
                {
                    _context.ProductAttributes.Remove(attribute);
                }
            }

            // Mettre à jour les variantes
            if (request.Variants != null && request.Variants.Any())
            {
                // Conserver toutes les variantes existantes pour le moment
                // Nous allons les mettre à jour ou les supprimer en fonction des données reçues
                var existingVariants = product.Variants.ToList();
                
                foreach (var variantRequest in request.Variants)
                {
                    if (variantRequest.Id > 0)
                    {
                        // Mettre à jour une variante existante
                        var existingVariant = existingVariants
                            .FirstOrDefault(v => v.Id == variantRequest.Id);

                        if (existingVariant != null)
                        {
                            existingVariant.Name = variantRequest.Name ?? existingVariant.Name;
                            existingVariant.Sku = variantRequest.Sku ?? existingVariant.Sku;
                            existingVariant.Price = variantRequest.Price ?? existingVariant.Price;
                            existingVariant.StockQuantity = variantRequest.StockQuantity ?? existingVariant.StockQuantity;
                            existingVariant.IsActive = true;
                            existingVariant.UpdatedAt = DateTime.UtcNow;
                        }
                    }
                    else
                    {
                        // Ajouter une nouvelle variante
                        var newVariant = new ProductVariant
                        {
                           
                            ProductId = product.Id,
                            Name = variantRequest.Name,
                            Sku = variantRequest.Sku,
                            Price = variantRequest.Price.Value,
                            StockQuantity = variantRequest.StockQuantity.Value,
                            IsActive = true,
                            CreatedAt = DateTime.UtcNow,
                            UpdatedAt = DateTime.UtcNow
                        };

                        await _context.ProductVariants.AddAsync(newVariant);
                    }
                }
                
                // Supprimer les variantes qui ne sont pas dans la liste de mise à jour
                var variantIdsToKeep = request.Variants
                    .Where(v => v.Id > 0)
                    .Select(v => v.Id)
                    .ToList();
                
                foreach (var variant in existingVariants)
                {
                    if (!variantIdsToKeep.Contains(variant.Id))
                    {
                        _context.ProductVariants.Remove(variant);
                    }
                }
            }
            else
            {
                // Si la liste de variantes est vide, supprimer toutes les variantes existantes
                foreach (var variant in product.Variants.ToList())
                {
                    _context.ProductVariants.Remove(variant);
                }
            }

            await _context.SaveChangesAsync();
            return await GetProductByIdAsync(product.Id);
        }

        public async Task<bool> DeleteProductAsync(int id)
        {
            try
            {
                var product = await _context.Products
                    .Include(p => p.Images)
                    .Include(p => p.Variants)
                    .Include(p => p.Attributes)
                    .FirstOrDefaultAsync(p => p.Id == id);
                
                if (product == null)
                {
                    return false;
                }

                // Supprimer d'abord les entités associées pour éviter les violations de contrainte
                if (product.Images != null && product.Images.Any())
                {
                    _context.ProductImages.RemoveRange(product.Images);
                }
                
                if (product.Variants != null && product.Variants.Any())
                {
                    _context.ProductVariants.RemoveRange(product.Variants);
                }
                
                if (product.Attributes != null && product.Attributes.Any())
                {
                    _context.ProductAttributes.RemoveRange(product.Attributes);
                }

                // Puis supprimer le produit
                _context.Products.Remove(product);
                await _context.SaveChangesAsync();
                return true;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la suppression du produit {ProductId}", id);
                throw; // Propager l'exception pour qu'elle soit gérée par le contrôleur
            }
        }

        public async Task<ProductDto> GetProductByIdAsync(int id)
        {
            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Attributes)
                .Include(p => p.Variants)
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == id && p.IsActive && p.ApprovalStatus == Domain.Enums.ProductApprovalStatus.Approved);

            if (product == null)
            {
                throw new Exception($"Product with ID {id} not found or not approved");
            }

            return _mapper.Map<ProductDto>(product);
        }

        // Méthode pour les portails admin et vendeur qui peuvent voir tous les produits
        public async Task<ProductDto> GetProductByIdForManagementAsync(int id)
        {
            var product = await _context.Products
                .Include(p => p.Category)
                .Include(p => p.Attributes)
                .Include(p => p.Variants)
                .Include(p => p.Images)
                .Include(p => p.Seller)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                throw new Exception($"Product with ID {id} not found");
            }

            return _mapper.Map<ProductDto>(product);
        }

        public async Task<IEnumerable<ProductDto>> GetAllProductsAsync()
        {
            var products = await _context.Products
                .Include(p => p.Images)
                .Include(p => p.Variants)
                .Include(p => p.Attributes)
                .ToListAsync();

            return _mapper.Map<IEnumerable<ProductDto>>(products);
        }

        public async Task<PagedResultDto<ProductDto>> SearchProductsAsync(ProductSearchDto searchDto)
        {
            var query = _context.Products
                .Include(p => p.Images)
                .Include(p => p.Variants)
                .Include(p => p.Attributes)
                .Where(p => p.IsActive && p.ApprovalStatus == Domain.Enums.ProductApprovalStatus.Approved) // Filtrer par produits actifs et approuvés
                .AsQueryable();

            // Apply filters
            if (!string.IsNullOrEmpty(searchDto.SearchTerm))
            {
                var searchTerm = searchDto.SearchTerm.ToLower();
                query = query.Where(p => p.Name.ToLower().Contains(searchTerm) ||
                                         p.Description.ToLower().Contains(searchTerm));
            }

            if (searchDto.CategoryIds != null && searchDto.CategoryIds.Any())
            {
                query = query.Where(p => searchDto.CategoryIds.Contains(p.CategoryId));
            }
            else if (searchDto.CategoryId.HasValue)
            {
                query = query.Where(p => p.CategoryId == searchDto.CategoryId.Value);
            }
            
            if (searchDto.SellerId.HasValue)
            {
                query = query.Where(p => p.SellerId == searchDto.SellerId.Value);
            }

            if (searchDto.MinPrice.HasValue)
            {
                query = query.Where(p => p.Price >= searchDto.MinPrice.Value);
            }

            if (searchDto.MaxPrice.HasValue)
            {
                query = query.Where(p => p.Price <= searchDto.MaxPrice.Value);
            }

            if (!string.IsNullOrEmpty(searchDto.Brand))
            {
                query = query.Where(p => p.Brand == searchDto.Brand);
            }

            if (searchDto.IsActive.HasValue)
            {
                query = query.Where(p => p.IsActive == searchDto.IsActive.Value);
            }

            if (searchDto.IsFeatured.HasValue)
            {
                query = query.Where(p => p.IsFeatured == searchDto.IsFeatured.Value);
            }

            if (searchDto.InStockOnly)
            {
                query = query.Where(p => p.StockQuantity > 0);
            }

            var totalCount = await query.CountAsync();
            
            // Apply pagination
            var products = await query
                .Skip((searchDto.Page - 1) * searchDto.PageSize)
                .Take(searchDto.PageSize)
                .ToListAsync();

            var productDtos = _mapper.Map<List<ProductDto>>(products);

            return new PagedResultDto<ProductDto>
            {
                Items = productDtos,
                TotalCount = totalCount,
                Page = searchDto.Page,
                PageSize = searchDto.PageSize
            };
        }

        public async Task<ProductDto> AddProductImageAsync(int productId, CreateProductImageRequest request)
        {
            var product = await _context.Products
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                throw new Exception($"Product with ID {productId} not found");
            }

            // Créer une instance de ProductImage directement
            var image = new ProductImage
            {
                ProductId = productId,
                ImageUrl = $"/api/products/{productId}/images/{Guid.NewGuid()}", // URL unique pour l'image
                ThumbnailUrl = request.Image, // Stocker les données base64 dans ImageData
                IsMain = request.IsMain,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.ProductImages.Add(image);
            await _context.SaveChangesAsync();

            return await GetProductByIdAsync(productId);
        }

        public async Task<ProductDto> DeleteProductImageAsync(int productId, int imageId)
        {
            var product = await _context.Products
                .Include(p => p.Images)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                throw new Exception($"Product with ID {productId} not found");
            }

            var image = product.Images.FirstOrDefault(i => i.Id == imageId);
            if (image == null)
            {
                throw new Exception($"Image with ID {imageId} not found for product {productId}");
            }

            _context.ProductImages.Remove(image);
            await _context.SaveChangesAsync();

            return await GetProductByIdAsync(productId);
        }

        public async Task<ProductDto> AddProductVariantAsync(int productId, CreateProductVariantRequest request)
        {
            var product = await _context.Products
                .Include(p => p.Variants)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                throw new Exception($"Product with ID {productId} not found");
            }

            var variant = new ProductVariant
            {
                
                ProductId = productId,
                Name = request.Name,
                Sku = request.Sku,
                Price = request.Price,
                StockQuantity = request.StockQuantity,
                IsActive = true,
                CreatedAt = DateTime.UtcNow,
                UpdatedAt = DateTime.UtcNow
            };

            _context.ProductVariants.Add(variant);
            await _context.SaveChangesAsync();

            return await GetProductByIdAsync(productId);
        }

        public async Task<ProductDto> UpdateProductVariantAsync(int productId, int variantId, UpdateProductVariantRequest request)
        {
            var product = await _context.Products
                .Include(p => p.Variants)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                throw new Exception($"Product with ID {productId} not found");
            }

            var variant = product.Variants.FirstOrDefault(v => v.Id == variantId);
            if (variant == null)
            {
                throw new Exception($"Variant with ID {variantId} not found for product {productId}");
            }

            variant.Name = request.Name ?? variant.Name;
            variant.Sku = request.Sku ?? variant.Sku;
            variant.Price = request.Price ?? variant.Price;
            variant.StockQuantity = request.StockQuantity ?? variant.StockQuantity;
            variant.IsActive = true;
            variant.UpdatedAt = DateTime.UtcNow;

            _context.ProductVariants.Update(variant);
            await _context.SaveChangesAsync();

            return await GetProductByIdAsync(productId);
        }

        public async Task<ProductDto> DeleteProductVariantAsync(int productId, int variantId)
        {
            var product = await _context.Products
                .Include(p => p.Variants)
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                throw new Exception($"Product with ID {productId} not found");
            }

            var variant = product.Variants.FirstOrDefault(v => v.Id == variantId);
            if (variant == null)
            {
                throw new Exception($"Variant with ID {variantId} not found for product {productId}");
            }

            _context.ProductVariants.Remove(variant);
            await _context.SaveChangesAsync();

            return await GetProductByIdAsync(productId);
        }

        public async Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId)
        {
            var products = await _context.Products
                .Where(p => p.CategoryId == categoryId && p.IsActive && p.ApprovalStatus == Domain.Enums.ProductApprovalStatus.Approved)
                .Include(p => p.Images)
                .Include(p => p.Variants)
                .Include(p => p.Attributes)
                .ToListAsync();

            return _mapper.Map<IEnumerable<ProductDto>>(products);
        }

        public async Task<IEnumerable<ProductDto>> GetProductsByCategoryAsync(int categoryId, int page, int pageSize)
        {
            var products = await _context.Products
                .Where(p => p.CategoryId == categoryId && p.IsActive && p.ApprovalStatus == Domain.Enums.ProductApprovalStatus.Approved)
                .Include(p => p.Images)
                .Include(p => p.Variants)
                .Include(p => p.Attributes)
                .Include(p => p.Category)
                .Include(p => p.Seller)
                .OrderByDescending(p => p.CreatedAt)
                .Skip((page - 1) * pageSize)
                .Take(pageSize)
                .ToListAsync();

            var productDtos = _mapper.Map<IEnumerable<ProductDto>>(products);

            // S'assurer que chaque produit a une URL d'image principale
            foreach (var productDto in productDtos)
            {
                if (productDto.Images != null && productDto.Images.Any())
                {
                    var mainImage = productDto.Images.FirstOrDefault(i => i.IsMain);
                    productDto.MainImageUrl = mainImage?.Url ?? productDto.Images.First().Url;
                }
                else
                {
                    productDto.MainImageUrl = "/images/placeholder.png";
                }
            }

            return productDtos;
        }

        public async Task<IEnumerable<ProductDto>> GetFeaturedProductsAsync(int count)
        {
            try
            {
                _logger.LogInformation($"Récupération des {count} produits en vedette");

                // Récupérer tous les produits actifs et approuvés
                var products = await _context.Products
                    .Where(p => p.IsActive && p.ApprovalStatus == Domain.Enums.ProductApprovalStatus.Approved)
                    .Include(p => p.Images)
                    .Include(p => p.Category)
                    .Include(p => p.Seller)
                    .Include(p => p.Variants)
                    .Include(p => p.Attributes)
                    .OrderByDescending(p => p.CreatedAt)
                    .Take(count)
                    .ToListAsync();

                _logger.LogInformation($"Nombre de produits récupérés: {products.Count}");
                
                var productDtos = _mapper.Map<IEnumerable<ProductDto>>(products);
                
                // S'assurer que chaque produit a une URL d'image principale
                foreach (var productDto in productDtos)
                {
                    if (productDto.Images != null && productDto.Images.Any())
                    {
                        var mainImage = productDto.Images.FirstOrDefault(i => i.IsMain);
                        productDto.MainImageUrl = mainImage?.Url ?? productDto.Images.First().Url;
                    }
                    else
                    {
                        productDto.MainImageUrl = "/images/placeholder.png";
                    }
                }

                return productDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des produits en vedette");
                return new List<ProductDto>();
            }
        }

        public async Task<IEnumerable<ProductDto>> GetNewProductsAsync(int count)
        {
            try
            {
                _logger.LogInformation($"Récupération des {count} nouveaux produits");
                
                var products = await _context.Products
                    .Where(p => p.IsActive)
                    .Include(p => p.Images)
                    .Include(p => p.Category)
                    .Include(p => p.Seller)
                    .Include(p => p.Variants)
                    .Include(p => p.Attributes)
                    .OrderByDescending(p => p.CreatedAt)
                    .Take(count)
                    .ToListAsync();

                _logger.LogInformation($"Nombre de nouveaux produits récupérés: {products.Count}");
                
                var productDtos = _mapper.Map<IEnumerable<ProductDto>>(products);
                
                // S'assurer que chaque produit a une URL d'image principale
                foreach (var productDto in productDtos)
                {
                    if (productDto.Images != null && productDto.Images.Any())
                    {
                        var mainImage = productDto.Images.FirstOrDefault(i => i.IsMain);
                        productDto.MainImageUrl = mainImage?.Url ?? productDto.Images.First().Url;
                    }
                    else
                    {
                        productDto.MainImageUrl = "/images/placeholder.png";
                    }
                }

                return productDtos;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Erreur lors de la récupération des nouveaux produits");
                return new List<ProductDto>();
            }
        }

        public async Task<IEnumerable<ProductDto>> GetRelatedProductsAsync(int productId, int count)
        {
            var product = await _context.Products
                .FirstOrDefaultAsync(p => p.Id == productId);

            if (product == null)
            {
                return new List<ProductDto>();
            }

            // Obtenir des produits de la même catégorie
            var relatedProducts = await _context.Products
                .Where(p => p.CategoryId == product.CategoryId && p.Id != productId && p.IsActive && p.ApprovalStatus == Domain.Enums.ProductApprovalStatus.Approved)
                .Include(p => p.Images)
                .Include(p => p.Variants)
                .Include(p => p.Attributes)
                .OrderByDescending(p => p.CreatedAt)
                .Take(count)
                .ToListAsync();

            return _mapper.Map<IEnumerable<ProductDto>>(relatedProducts);
        }

        public async Task<ProductDto> ApproveProductAsync(int id, string approvedBy)
        {
            var product = await _context.Products
                .Include(p => p.Images)
                .Include(p => p.Variants)
                .Include(p => p.Attributes)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                throw new Exception($"Product with ID {id} not found");
            }

            product.ApprovalStatus = Domain.Enums.ProductApprovalStatus.Approved;
            product.ApprovedAt = DateTime.UtcNow;
            product.ApprovedBy = approvedBy;
            product.RejectionReason = null; // Effacer la raison de rejet si elle existait
            product.UpdatedAt = DateTime.UtcNow;

            _context.Products.Update(product);
            await _context.SaveChangesAsync();

            return _mapper.Map<ProductDto>(product);
        }

        public async Task<ProductDto> RejectProductAsync(int id, string rejectionReason, string rejectedBy)
        {
            var product = await _context.Products
                .Include(p => p.Images)
                .Include(p => p.Variants)
                .Include(p => p.Attributes)
                .FirstOrDefaultAsync(p => p.Id == id);

            if (product == null)
            {
                throw new Exception($"Product with ID {id} not found");
            }

            product.ApprovalStatus = Domain.Enums.ProductApprovalStatus.Rejected;
            product.RejectionReason = rejectionReason;
            product.ApprovedAt = null;
            product.ApprovedBy = rejectedBy;
            product.UpdatedAt = DateTime.UtcNow;

            _context.Products.Update(product);
            await _context.SaveChangesAsync();

            return _mapper.Map<ProductDto>(product);
        }
    }
}
